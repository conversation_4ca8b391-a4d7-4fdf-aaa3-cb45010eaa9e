<?php

/*
* Plugin Name: MEDYA Uniasselvi Button
* Plugin URI:https://medya.com.br
* Description: Exibe o formulário que redireciona para a página de inscrição.
* Version: 1.0.0
* Author: <PERSON>
* Author URI: https://giuseppesilva.com.br/
* Text Domain: medya-uniasselvi-button
*/

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

final class Medya_Uniasselvi_Button {
    /**
    * Plugin Version
    *
    * @var string The plugin version.
    * @since 1.0.0
    */
    const VERSION = '1.0.0';
    
    /**
    * Minimum PHP Version
    *
    * @var string Minimum PHP version required to run the plugin.
    * @since 1.0.0
    */
    const MINIMUM_PHP_VERSION = '7.4';
    
    /**
    * Instance
    *
    * @access private
    * @static
    * @var Medya_Uniasselvi_Button The single instance of the class.
    * @since 1.0.0
    */
    private static $_instance = null;
    
    /**
    * Instance
    *
    * Ensures only one instance of the class is loaded or can be loaded.
    *
    * @access public
    * @static
    * @since 1.0.0
    *
    * @return Medya_<PERSON>iasselvi_Button An instance of the class.
    */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        
        return self::$_instance;
    }
    
    /**
    * Constructor
    *
    * @access public
    * @since 1.0.0
    */
    public function __construct() {
        add_action('plugins_loaded', [$this, 'init']);
    }
    
    /**
    * Initialize the plugin
    *
    * Load the plugin only after Elementor (and other plugins) are loaded.
    * Checks for basic plugin requirements, if one check fail don't continue,
    * if all check have passed load the files required to run the plugin.
    *
    * Fired by `plugins_loaded` action hook.
    *
    * @access public
    * @since 1.0.0
    */
    public function init() {
        // Check for required PHP version
        if (version_compare(PHP_VERSION, self::MINIMUM_PHP_VERSION, '<')) {
            add_action('admin_notices', [$this, 'admin_notice_minimum_php_version']);
            
            return;
        }
        
        add_shortcode('medya_form', [$this, 'medya_form_func']);
    }
    
    /**
    * Admin notice
    *
    * Warning when the site doesn't have a minimum required PHP version.
    *
    * @access public
    * @since 1.0.0
    */
    public function admin_notice_minimum_php_version() {
        if ( isset( $_GET['activate'] ) ) {
            unset( $_GET['activate'] );
        }
        
        $message = sprintf(
            /* translators: 1: Plugin name 2: PHP 3: Required PHP version */
            esc_html__( '"%1$s" requires "%2$s" version %3$s or greater.', 'medya-afiliado' ),
            '<strong>' . esc_html__( 'MEDYA Afiliado', 'medya-afiliado' ) . '</strong>',
            '<strong>' . esc_html__( 'PHP', 'medya-afiliado' ) . '</strong>',
            self::MINIMUM_PHP_VERSION
        );
        
        printf( '<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message );
    }
    
    /**
    * Register Meta Fields
    *
    * Register post meta fields
    *
    * @access public
    * @since 1.0.0
    */
    function medya_form_func() {
        return get_post_meta(get_the_ID(), 'form', true);
    }
}

Medya_Uniasselvi_Button::instance();